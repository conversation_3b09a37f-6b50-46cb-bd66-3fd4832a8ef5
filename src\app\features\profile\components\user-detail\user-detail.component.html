<!-- User Detail Content -->
<div class="max-w-4xl">
  <!-- Header -->
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center">
      <button
        (click)="goBack()"
        class="mr-4 p-2 bg-slate-700 hover:bg-slate-600 text-gray-300 rounded-lg transition-colors"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"></path>
        </svg>
      </div>
      <div>
        <h1 class="text-4xl font-bold text-white mb-2">
          {{ user ? 'Пользователь #' + user.id : 'Загрузка...' }}
        </h1>
        <p class="text-gray-300">Просмотр и редактирование данных пользователя</p>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div *ngIf="user && !loading" class="flex space-x-3">
      <button
        *ngIf="!isEditing"
        (click)="toggleEdit()"
        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
      >
        ✏️ Редактировать
      </button>
      
      <button
        (click)="deleteUser()"
        class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
      >
        🗑️ Удалить
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-8">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
    <p class="text-red-400">{{ error }}</p>
    <button (click)="loadUser(user?.id || 0)" class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
      Повторить попытку
    </button>
  </div>

  <!-- User Details -->
  <div *ngIf="user && !loading && !error">
    <!-- User Info Card -->
    <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-white">Информация о пользователе</h2>
        <div class="flex items-center space-x-4">
          <!-- User Code Badge -->
          <span class="inline-flex px-3 py-1 text-sm font-semibold rounded bg-blue-900/50 text-blue-400 border border-blue-500/50">
            {{ user.user_code }}
          </span>
          <!-- Role Badge -->
          <span [ngClass]="getUserRoleClass(user)" class="text-lg font-medium">
            {{ getUserRoleText(user) }}
          </span>
          <!-- Status Badge -->
          <span [ngClass]="user.is_active ? 'bg-green-900/50 text-green-400 border-green-500/50' : 'bg-red-900/50 text-red-400 border-red-500/50'"
                class="inline-flex px-3 py-1 text-sm font-semibold rounded-full border">
            {{ user.is_active ? 'Активен' : 'Неактивен' }}
          </span>
        </div>
      </div>

      <!-- Form -->
      <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Email -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
            <input
              type="email"
              formControlName="email"
              [readonly]="!isEditing"
              [ngClass]="isEditing ? 'bg-slate-800/60 border-slate-600/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent' : 'bg-slate-700/30 border-slate-600/30 cursor-not-allowed'"
              class="w-full px-3 py-2 border rounded-lg text-white placeholder-gray-400 focus:outline-none transition-colors"
            >
            <div *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched" class="mt-1 text-red-400 text-xs">
              Email обязателен и должен быть корректным
            </div>
          </div>

          <!-- Phone -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Телефон</label>
            <input
              type="tel"
              formControlName="phone"
              [readonly]="!isEditing"
              [ngClass]="isEditing ? 'bg-slate-800/60 border-slate-600/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent' : 'bg-slate-700/30 border-slate-600/30 cursor-not-allowed'"
              class="w-full px-3 py-2 border rounded-lg text-white placeholder-gray-400 focus:outline-none transition-colors"
              placeholder="Не указан"
            >
          </div>

          <!-- Password -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-300 mb-2">Пароль</label>
            <input
              type="password"
              formControlName="password"
              [readonly]="!isEditing"
              [ngClass]="isEditing ? 'bg-slate-800/60 border-slate-600/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent' : 'bg-slate-700/30 border-slate-600/30 cursor-not-allowed'"
              class="w-full px-3 py-2 border rounded-lg text-white placeholder-gray-400 focus:outline-none transition-colors"
              placeholder="Оставьте пустым, чтобы не изменять пароль"
            >
            <p class="mt-1 text-gray-400 text-xs">Оставьте поле пустым, если не хотите изменять пароль</p>
          </div>

          <!-- Active Status -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Статус активности</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="true"
                  formControlName="is_active"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-green-400">Активен</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="false"
                  formControlName="is_active"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-red-400">Неактивен</span>
              </label>
            </div>
          </div>

          <!-- Staff Status -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Статус администратора</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="true"
                  formControlName="is_staff"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-yellow-400">Администратор</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="false"
                  formControlName="is_staff"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-blue-400">Пользователь</span>
              </label>
            </div>
          </div>

          <!-- Superuser Status -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-300 mb-2">Статус суперпользователя</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="true"
                  formControlName="is_superuser"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-red-400">Суперпользователь</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  [value]="false"
                  formControlName="is_superuser"
                  [disabled]="!isEditing"
                  class="mr-2 text-blue-600 focus:ring-blue-500"
                >
                <span class="text-gray-400">Обычный пользователь</span>
              </label>
            </div>
            <p class="mt-1 text-yellow-400 text-xs">⚠️ Суперпользователи имеют полный доступ ко всем функциям системы</p>
          </div>
        </div>



        <!-- Form Actions -->
        <div *ngIf="isEditing" class="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            (click)="toggleEdit()"
            [disabled]="saving"
            class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            Отмена
          </button>
          <button
            type="submit"
            [disabled]="userForm.invalid || saving"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center"
          >
            <span *ngIf="saving" class="mr-2">
              <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ saving ? 'Сохранение...' : 'Сохранить изменения' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Additional Info Card -->
    <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6">
      <h3 class="text-xl font-bold text-white mb-4">Дополнительная информация</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">Дата регистрации</label>
          <p class="text-white">{{ formatDate(user.date_joined) }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-1">Последний вход</label>
          <p class="text-white">{{ user.last_login ? formatDate(user.last_login) : 'Никогда' }}</p>
        </div>
      </div>
    </div>
  </div>
</div>
