export interface User {
  id: number;
  email: string;
  phone: string | null;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  user_code: string;
  date_joined: string;
  last_login: string | null;
  password?: string; // Optional for security - not always returned from API
}

export interface UserListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: User[];
}

export interface UserFilters {
  search?: string;
  is_staff?: boolean;
  is_active?: boolean;
  is_superuser?: boolean;
  ordering?: string;
}

export interface UserUpdateRequest {
  email?: string;
  phone?: string;
  is_active?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  password?: string;
}
