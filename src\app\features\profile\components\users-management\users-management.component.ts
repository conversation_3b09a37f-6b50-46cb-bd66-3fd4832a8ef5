import { Component, OnInit, HostListener } from '@angular/core';
import { UserService } from '../../../../core/services/user.service';
import { ModalService } from '../../../../core/services/modal.service';
import { User, UserFilters } from '../../../../core/models/user.model';

@Component({
  selector: 'app-users-management',
  standalone: false,
  templateUrl: './users-management.component.html',
  styleUrl: './users-management.component.css'
})
export class UsersManagementComponent implements OnInit {
  users: User[] = [];
  usersLoading = false;
  usersError = '';
  totalUsers = 0;
  currentPage = 1;
  pageSize = 10;

  // Make Math available in template
  Math = Math;

  // Dropdown management
  openDropdownId: number | null = null;

  // Search and filtering
  searchTerm = '';
  selectedRole: 'all' | 'staff' | 'user' | 'superuser' = 'all';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  sortBy: 'date_joined' | '-date_joined' | 'email' | '-email' | 'user_code' | '-user_code' = '-date_joined';

  constructor(
    private userService: UserService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.usersLoading = true;
    this.usersError = '';

    const filters: UserFilters = {
      search: this.searchTerm || undefined,
      ordering: this.sortBy
    };

    // Add role filter
    if (this.selectedRole !== 'all') {
      if (this.selectedRole === 'superuser') {
        filters.is_superuser = true;
      } else if (this.selectedRole === 'staff') {
        filters.is_staff = true;
        filters.is_superuser = false;
      } else if (this.selectedRole === 'user') {
        filters.is_staff = false;
        filters.is_superuser = false;
      }
    }

    // Add status filter
    if (this.selectedStatus !== 'all') {
      filters.is_active = this.selectedStatus === 'active';
    }

    this.userService.getUsers(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.users = response.results;
        this.totalUsers = response.count;
        this.usersLoading = false;
      },
      error: (error: any) => {
        console.error('Failed to load users:', error);
        this.usersError = error.message || 'Failed to load users';
        this.usersLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onRoleFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUsers();
  }

  get totalPages(): number {
    return Math.ceil(this.totalUsers / this.pageSize);
  }

  get pages(): number[] {
    const totalPages = this.totalPages;
    const currentPage = this.currentPage;
    const pages: number[] = [];
    
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  getUserRoleText(user: User): string {
    if (user.is_superuser) return 'Суперпользователь';
    if (user.is_staff) return 'Администратор';
    return 'Пользователь';
  }

  getUserRoleClass(user: User): string {
    if (user.is_superuser) return 'text-red-400';
    if (user.is_staff) return 'text-yellow-400';
    return 'text-blue-400';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  toggleActiveStatus(user: User): void {
    this.userService.toggleUserStatus(user.id, !user.is_active).subscribe({
      next: (updatedUser: any) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
        this.modalService.success('Успех', 'Статус активности успешно обновлен');
      },
      error: (error: any) => {
        console.error('Failed to update active status:', error);
        let errorMessage = 'Не удалось обновить статус активности';

        if (error.message) {
          errorMessage += ': ' + error.message;
        }

        this.modalService.error('Ошибка', errorMessage);
      }
    });
  }

  toggleStaffStatus(user: User): void {
    this.userService.toggleStaffStatus(user.id, !user.is_staff).subscribe({
      next: (updatedUser: any) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
        this.modalService.success('Успех', 'Статус администратора успешно обновлен');
      },
      error: (error: any) => {
        console.error('Failed to update staff status:', error);
        let errorMessage = 'Не удалось обновить статус администратора';

        // Handle specific error messages
        if (error.message) {
          errorMessage += ': ' + error.message;
        }

        this.modalService.error('Ошибка', errorMessage);
      }
    });
  }

  deleteUser(user: User): void {
    // Prevent deletion of superusers
    if (user.is_superuser) {
      this.modalService.error('Ошибка', 'Суперпользователя нельзя удалить');
      return;
    }

    this.modalService.confirm(
      'Удаление пользователя',
      `Вы уверены, что хотите удалить пользователя ${user.email}?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.userService.deleteUser(user.id).subscribe({
          next: () => {
            this.users = this.users.filter(u => u.id !== user.id);
            this.totalUsers--;
            this.modalService.success('Успех', 'Пользователь успешно удален');
          },
          error: (error: any) => {
            console.error('Failed to delete user:', error);
            let errorMessage = 'Не удалось удалить пользователя';

            // Handle specific error for superuser deletion
            if (error.status === 403) {
              errorMessage = 'Суперпользователя нельзя удалить';
            } else if (error.message) {
              errorMessage += ': ' + error.message;
            }

            this.modalService.error('Ошибка', errorMessage);
          }
        });
      }
    });
  }

  // Dropdown management methods
  toggleDropdown(userId: number): void {
    this.openDropdownId = this.openDropdownId === userId ? null : userId;
  }

  closeDropdown(): void {
    this.openDropdownId = null;
  }

  // Close dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.closeDropdown();
    }
  }
}
